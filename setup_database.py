#!/usr/bin/env python3
"""
Database setup script for RainbowPlus.
Works with both local Docker PostgreSQL and Azure PostgreSQL.

Usage:
    # Local development
    python setup_database.py --env local
    
    # Azure production
    python setup_database.py --env azure
    
    # Custom environment file
    python setup_database.py --env-file .env.custom
"""

import os
import sys
import argparse
from pathlib import Path
import logging

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_environment(env_name: str = None, env_file: str = None):
    """Load environment variables from file."""
    if env_file:
        env_path = Path(env_file)
    elif env_name:
        env_path = Path(f".env.{env_name}")
    else:
        env_path = Path(".env.local")
    
    if not env_path.exists():
        logger.error(f"❌ Environment file not found: {env_path}")
        logger.info("💡 Available environment files:")
        for env_file in Path(".").glob(".env.*"):
            logger.info(f"   {env_file}")
        return False
    
    logger.info(f"📁 Loading environment from: {env_path}")
    
    # Load environment variables
    with open(env_path) as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key] = value
    
    return True


def test_database_connection():
    """Test database connection."""
    try:
        from rainbowplus.api.database import engine
        
        logger.info("🔗 Testing database connection...")
        
        with engine.connect() as conn:
            result = conn.execute("SELECT version()")
            version = result.fetchone()[0]
            logger.info(f"✅ Database connection successful!")
            logger.info(f"   PostgreSQL version: {version}")
            
            # Check if it's Azure PostgreSQL
            if 'azure' in version.lower():
                logger.info("☁️  Connected to Azure PostgreSQL")
            else:
                logger.info("🐳 Connected to local PostgreSQL")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False


def create_tables():
    """Create database tables."""
    try:
        from rainbowplus.api.models import Base
        from rainbowplus.api.database import engine
        
        logger.info("🏗️  Creating database tables...")
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        
        logger.info("✅ Database tables created successfully!")
        
        # List created tables
        from sqlalchemy import inspect
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        logger.info(f"📊 Created tables: {', '.join(tables)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create tables: {e}")
        return False


def add_nickname_column():
    """Add nickname column if it doesn't exist."""
    try:
        from rainbowplus.api.database import engine
        from sqlalchemy import text
        
        logger.info("🔧 Checking for nickname column...")
        
        with engine.connect() as conn:
            # Check if nickname column exists
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'rainbow_analysis_jobs' 
                AND column_name = 'nickname'
            """))
            
            if result.fetchone():
                logger.info("✅ Nickname column already exists")
                return True
            
            # Add the nickname column
            logger.info("➕ Adding nickname column...")
            conn.execute(text("""
                ALTER TABLE rainbow_analysis_jobs 
                ADD COLUMN nickname VARCHAR(255)
            """))
            conn.commit()
            
            logger.info("✅ Nickname column added successfully!")
            return True
            
    except Exception as e:
        logger.error(f"❌ Failed to add nickname column: {e}")
        return False


def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(description="Setup RainbowPlus database")
    parser.add_argument("--env", choices=["local", "azure"], help="Environment preset")
    parser.add_argument("--env-file", help="Custom environment file path")
    parser.add_argument("--skip-tables", action="store_true", help="Skip table creation")
    parser.add_argument("--test-only", action="store_true", help="Only test connection")
    
    args = parser.parse_args()
    
    print("🚀 RainbowPlus Database Setup")
    print("   Setting up database for local development and Azure production")
    print()
    
    # Load environment
    if not load_environment(args.env, args.env_file):
        return False
    
    # Test connection
    if not test_database_connection():
        logger.error("💡 Troubleshooting tips:")
        logger.error("   - Check if PostgreSQL is running")
        logger.error("   - Verify connection parameters in environment file")
        logger.error("   - For Azure: ensure firewall allows your IP")
        logger.error("   - For local: ensure Docker container is running")
        return False
    
    if args.test_only:
        logger.info("🎉 Connection test completed successfully!")
        return True
    
    # Create tables
    if not args.skip_tables:
        if not create_tables():
            return False
    
    # Add nickname column (for existing databases)
    if not add_nickname_column():
        return False
    
    print()
    print("🎉 Database setup completed successfully!")
    print()
    print("💡 Next steps:")
    print("   1. Start the API server: uvicorn rainbowplus.api.app:app --host 0.0.0.0 --port 8000")
    print("   2. Test with: python test_slack_notification.py")
    print("   3. Configure Slack webhook for notifications")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
