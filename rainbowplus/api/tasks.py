import os
import ssl
from celery import Celery
from celery import shared_task
import celery_healthcheck
from typing import List, Optional, Dict, Any
import argparse
from rainbowplus.rainbowplus import (
    ConfigurationLoader,
    parse_arguments,
    run_rainbowplus,
)
from rainbowplus.api.logic import _process_results_logic
from rainbowplus.utils import initialize_language_models
from rainbowplus.scores import BleuScoreNLTK, create_openai_guard
from rainbowplus.api.notifications import slack_service
# Initialize Celery
# Determine environment (Azure or local)
REDIS_HOST = os.environ.get("REDIS_HOST", "localhost")
REDIS_PORT = os.environ.get("REDIS_PORT", "6379")
REDIS_KEY = os.environ.get("REDIS_KEY", None)  # Azure Cache for Redis access key
REDIS_TLS = os.environ.get("REDIS_TLS", "false").lower() == "true"

# Construct Redis URL based on environment
if REDIS_TLS and REDIS_KEY:
    # Azure Cache for Redis: use rediss:// and include access key
    redis_url = f"rediss://:{REDIS_KEY}@{REDIS_HOST}:{REDIS_PORT}/1"
else:
    # Local Redis: use redis:// without TLS
    redis_url = f"redis://{REDIS_HOST}:{REDIS_PORT}/0"

# celery_app = Celery('rainbow_analysis', backend='redis', broker=f'redis://{os.environ.get("REDIS_HOST", "localhost")}:{os.environ.get("REDIS_PORT", 6379)}/0')
# SSL configuration for Azure Cache for Redis
ssl_params = {
    "ssl_cert_reqs": ssl.CERT_REQUIRED if REDIS_TLS else None,  # Required for rediss://
    "ssl_ca_certs": None,  # Use default system CA certificates
} if REDIS_TLS else None

# Initialize Celery
celery_app = Celery(
    'rainbow_analysis',
    backend=redis_url,
    broker=redis_url,
    broker_use_ssl=ssl_params,
    redis_backend_use_ssl=ssl_params,
)

celery_healthcheck.register(celery_app)
def make_json_serializable(obj):
    """Convert objects to JSON-serializable format."""
    if hasattr(obj, '__dict__'):
        # Convert objects with __dict__ to dictionaries
        return {key: make_json_serializable(value) for key, value in obj.__dict__.items()}
    elif isinstance(obj, list):
        return [make_json_serializable(item) for item in obj]
    elif isinstance(obj, dict):
        # Convert dictionary keys to strings and recursively process values
        return {
            str(key): make_json_serializable(value)
            for key, value in obj.items()
        }
    elif isinstance(obj, tuple):
        # Convert tuples to strings for JSON compatibility
        return str(obj)
    else:
        # Return primitive types as-is
        return obj

@celery_app.task
def run_analysis_task(
    prompts: List[str],
    target_llm: str,
    num_samples: int,
    num_mutations: int,
    max_iters: int,
    api_key: Optional[str] = None,
    base_url: Optional[str] = None
) -> Dict[str, Any]:
    """
    Simple background task to run analysis
    
    - Moves heavy computation to background
    - Minimal task logic
    - Delegates to existing logic
    """
    try:                
        # Prepare arguments
        # args = parse_arguments()
        args = argparse.Namespace(
        num_samples=150,
        max_iters=5,
        sim_threshold=0.6,
        num_mutations=5,
        fitness_threshold=0.5,
        config_file="./configs/base.yml",
        log_dir="./logs",
        log_interval=50,
        dataset="./data/do-not-answer.json",
        target_llm="Qwen/Qwen2.5-0.5B-Instruct"
    )
        args.num_samples = num_samples
        args.num_mutations = num_mutations
        args.max_iters = max_iters
        args.api_key = api_key
        args.base_url = base_url
        
        # Load configuration
        config = ConfigurationLoader.load(args.config_file)
        config.target_llm.model_kwargs["model"] = target_llm
        config.target_llm.api_key = api_key
        config.target_llm.base_url = base_url
        
        # Initialize components
        llms = initialize_language_models(config)
        fitness_fn = create_openai_guard(config.fitness_llm)
        similarity_fn = BleuScoreNLTK()
        
        # Run analysis
        adv_prompts, responses, scores = run_rainbowplus(
            args, config, prompts, llms, fitness_fn, similarity_fn, custom_prompts=None
        )
        
        # Process results
        points = _process_results_logic(adv_prompts, responses, scores)

        # Convert PointResponse objects to dictionaries for JSON serialization
        points_dict = [
            {
                "adv_prompt": point.adv_prompt,
                "response": point.response,
                "score": point.score,
                "descriptor": point.descriptor
            }
            for point in points
        ]

        # Convert Archive scores to dictionary for JSON serialization and notifications
        scores_dict = {str(key): value for key, value in scores._archive.items()}

        return {
            "success": True,
            "points": points_dict,  # Use serializable dictionaries
            "scores": scores_dict,  # Convert Archive to dict for notification
            "message": "Analysis completed successfully"
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Analysis failed",
            "points": [],
            "scores": {}
        }

# Optional: Checkpointed version for long-running tasks
@shared_task
def run_checkpointed_task(
    job_id: str,
    prompts: List[str],
    target_llm: str,
    num_samples: int,
    num_mutations: int,
    max_iters: int,
    api_key: Optional[str] = None,
    base_url: Optional[str] = None
) -> Dict[str, Any]:
    """
    Checkpointed version with progress tracking
    """
    from .models import RainbowAnalysisJob
    from .database import get_db_session
    
    try:
        # Update job status
        with get_db_session() as session:
            job = session.query(RainbowAnalysisJob).filter_by(id=job_id).first()
            job.status = 'processing'
        
        # Run main analysis (similar to above)
        result = run_analysis_task(
            prompts, target_llm, num_samples, 
            num_mutations, max_iters, 
            api_key, base_url
        )
        
        # Update final status and send notification
        with get_db_session() as session:
            job = session.query(RainbowAnalysisJob).filter_by(id=job_id).first()

            # Check if the analysis actually succeeded or failed
            if result.get('success'):
                job.status = 'completed'
                job.results = make_json_serializable(result)  # Ensure JSON serializable

                # Send success notification if nickname is provided
                if job.nickname:
                    score_results = result.get('scores', {})
                    slack_service.send_completion_notification(
                        nickname=job.nickname,
                        job_id=str(job_id),
                        status='completed',
                        prompts=job.prompts,
                        target_llm=job.target_llm,
                        target_llm_base_url=job.base_url,
                        num_samples=job.num_samples,
                        num_mutations=job.num_mutations,
                        max_iters=job.max_iters,
                        score_results=score_results
                    )
            else:
                # Analysis returned success=False, treat as failure
                job.status = 'failed'
                job.error_message = result.get('error', 'Unknown error')
                job.results = make_json_serializable(result)  # Store the failed result too

                # Send failure notification if nickname is provided
                if job.nickname:
                    slack_service.send_completion_notification(
                        nickname=job.nickname,
                        job_id=str(job_id),
                        status='failed',
                        prompts=job.prompts,
                        target_llm=job.target_llm,
                        target_llm_base_url=job.base_url,
                        num_samples=job.num_samples,
                        num_mutations=job.num_mutations,
                        max_iters=job.max_iters,
                        error_message=result.get('error', 'Unknown error')
                    )

        return result
    
    except Exception as e:
        # Update error status and send notification
        with get_db_session() as session:
            job = session.query(RainbowAnalysisJob).filter_by(id=job_id).first()
            job.status = 'failed'
            job.error_message = str(e)

            # Send Slack notification if nickname is provided
            if job.nickname:
                slack_service.send_completion_notification(
                    nickname=job.nickname,
                    job_id=str(job_id),
                    status='failed',
                    prompts=job.prompts,
                    target_llm=job.target_llm,
                    num_samples=job.num_samples,
                    num_mutations=job.num_mutations,
                    max_iters=job.max_iters,
                    error_message=str(e)
                )

        return {
            "success": False,
            "error": str(e),
            "message": "Checkpointed analysis failed"
        }