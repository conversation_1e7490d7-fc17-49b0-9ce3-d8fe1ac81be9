"use client"

import { SidebarInset, SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { But<PERSON> } from "@/components/ui/button"
import { Dashboard } from "@/components/sections/dashboard"
import { GenerateDataset } from "@/components/sections/generate-dataset"
import { AutomateTesting } from "@/components/sections/automate-testing"
import { Evaluation } from "@/components/sections/evaluation"
import { Protect } from "@/components/sections/protect"
import { Settings } from "@/components/sections/settings"
import { NewProjectDialog } from "@/components/new-project-dialog"
import { useState } from "react"

interface MainContentProps {
  currentSection: string
}

const sectionTitles = {
  dashboard: "Dashboard",
  "generate-dataset": "Generate Dataset",
  "automate-testing": "Automate Testing",
  evaluation: "Evaluation",
  protect: "Protect",
  settings: "Settings",
}

export function MainContent({ currentSection }: MainContentProps) {
  const [showNewProject, setShowNewProject] = useState(false)

  const renderSection = () => {
    switch (currentSection) {
      case "dashboard":
        return <Dashboard />
      case "generate-dataset":
        return <GenerateDataset />
      case "automate-testing":
        return <AutomateTesting />
      case "evaluation":
        return <Evaluation />
      case "protect":
        return <Protect />
      case "settings":
        return <Settings />
      default:
        return <Dashboard />
    }
  }

  return (
    <SidebarInset>
      <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <div className="flex flex-1 items-center justify-between w-full">
            <h1 className="text-xl font-semibold">{sectionTitles[currentSection as keyof typeof sectionTitles]}</h1>
            <Button onClick={() => setShowNewProject(true)}>New Project</Button>
          </div>
        </div>
      </header>
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">{renderSection()}</div>
      <NewProjectDialog open={showNewProject} onOpenChange={setShowNewProject} />
    </SidebarInset>
  )
}
